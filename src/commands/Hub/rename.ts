import BaseCommand from '#src/core/BaseCommand.js';
import Context from '#src/core/CommandContext/Context.js';
import { HubService } from '#src/services/HubService.js';
import { PremiumService } from '#src/lib/donations/core/PremiumService.js';
import { validatePremiumHubName } from '#src/utils/HubNameUtils.js';
import { runHubRoleChecksAndReply } from '#src/utils/hub/utils.js';
import Logger from '#src/utils/Logger.js';
import db from '#utils/Db.js';
import { getEmoji } from '#src/utils/EmojiUtils.js';
import { ApplicationCommandOptionType, type AutocompleteInteraction } from 'discord.js';
import HubCommand from './hub/index.js';
import { CacheManager } from '#src/managers/CacheManager.js';
import { DonationManager } from '#src/lib/donations/core/DonationManager.js';
import { getRedis } from '#src/utils/Redis.js';

const hubOption = {
  name: 'hub',
  description: 'The hub to rename.',
  type: ApplicationCommandOptionType.String,
  required: true,
  autocomplete: true,
} as const;

const nameOption = {
  name: 'name',
  description: 'The new custom display name (1-50 characters).',
  type: ApplicationCommandOptionType.String,
  required: true,
  max_length: 50,
} as const;

/**
 * Ergonomic command for hub name customization
 * Usage: /rename hub:MyHub name:New Display Name
 * Or: i.rename (interactive)
 */
export default class RenameCommand extends BaseCommand {
  private readonly hubService = new HubService();
  private readonly premiumService: PremiumService;

  constructor() {
    super({
      name: 'rename',
      description: '✨ Rename your hub with a custom display name (Premium)',
      types: { slash: true, prefix: true },
      options: [hubOption, nameOption],
      aliases: ['rn'],
    });

    // Initialize premium service with shared instances
    // Use existing pattern from codebase for manager instantiation
    const donationManager = new DonationManager();
    const cacheManager = new CacheManager(getRedis());
    this.premiumService = new PremiumService(donationManager, cacheManager);
  }

  async execute(ctx: Context) {
    const startTime = Date.now();

    try {
      const hubName = ctx.options.getString('hub', true);
      const customName = ctx.options.getString('name', true);

      // Find the hub
      const hubs = await this.hubService.findHubsByName(hubName);
      const hub = hubs.at(0);

      if (!hub) {
        await ctx.replyEmbed('❌ Hub not found. Please check the hub name and try again.', {
          flags: ['Ephemeral'],
        });
        return;
      }

      // Check if user is the hub owner
      if (!(await runHubRoleChecksAndReply(hub, ctx, { checkIfOwner: true }))) {
        return;
      }

      // Check premium status
      const hasPremium = await this.premiumService.canCustomizeHubName(ctx.user.id);
      if (!hasPremium) {
        await ctx.replyEmbed(
          '✨ **Premium Feature Required**\n\nCustom hub names are available to Ko-fi Supporters ($2.99/month).\n\n[Become a Supporter](https://ko-fi.com/interchat) to unlock this feature and support InterChat development!',
          {
            flags: ['Ephemeral'],
          },
        );
        return;
      }

      // Validate the new name
      const validation = validatePremiumHubName(customName);
      if (!validation.valid) {
        await ctx.replyEmbed(`❌ **Invalid Hub Name**\n\n${validation.error}`, {
          flags: ['Ephemeral'],
        });
        return;
      }

      // Sanitize the new name

      // Check if a hub with this name already exists
      const existingHub = await db.hub.findUnique({
        where: { name: customName },
        select: { id: true },
      });

      if (existingHub && existingHub.id !== hub.id) {
        await ctx.replyEmbed(
          `❌ **Name Already Taken**\n\nA hub with the name "${customName}" already exists. Please choose a different name.`,
          {
            flags: ['Ephemeral'],
          },
        );
        return;
      }

      // Update the hub name
      await db.hub.update({
        where: { id: hub.id },
        data: { name: customName },
      });

      // Log premium feature usage
      this.premiumService.logPremiumFeatureUsage(ctx.user.id, 'hub_name_customization', hub.id);

      // Success response
      await ctx.replyEmbed(
        `${getEmoji('tick_icon', ctx.client)} **Hub Renamed Successfully**\n\n**${hub.data.name}** now displays as **${customName}**\n\n${getEmoji('info_icon', ctx.client)} *This custom name appears in hub listings and messages.*`,
      );

      // Log performance
      const responseTime = Date.now() - startTime;
      Logger.info(`Hub rename completed in ${responseTime}ms`, {
        userId: ctx.user.id,
        hubId: hub.id,
        customName: customName,
        responseTime,
      });
    }
    catch (error) {
      Logger.error('Failed to rename hub', error);
      await ctx.replyEmbed('❌ An error occurred while renaming the hub. Please try again.', {
        flags: ['Ephemeral'],
      });
    }
  }

  async autocomplete(interaction: AutocompleteInteraction) {
    return await HubCommand.handleManagerCmdAutocomplete(interaction, this.hubService);
  }
}
