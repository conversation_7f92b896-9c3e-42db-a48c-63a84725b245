/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import {
  ActionRowBuilder,
  ButtonBuilder,
  ButtonStyle,
  Client,
  ComponentType,
  type EmbedBuilder,
  type Interaction,
  type InteractionReplyOptions,
  type InteractionResponse,
  type Message,
  type MessageComponentInteraction,
} from 'discord.js';
import { type EmojiKeys, getEmoji } from '#src/utils/EmojiUtils.js';

export interface PaginationOptions {
  embeds?: EmbedBuilder[];
  time?: number;
  client?: Client;
  ephemeral?: boolean;
  showPageNumbers?: boolean;
}

export interface PageOptions {
  embeds?: EmbedBuilder[];
  components?: ActionRowBuilder<ButtonBuilder>[];
}

export class Pagination {
  private pages: PageOptions[] = [];
  private readonly time: number;
  private readonly client: Client;
  private readonly ephemeral: boolean;
  private readonly showPageNumbers: boolean;
  private currentPage = 0;

  constructor(clientOrOptions: Client | PaginationOptions) {
    if (clientOrOptions instanceof Client) {
      // Legacy constructor: new Pagination(client)
      this.client = clientOrOptions;
      this.time = 60_000;
      this.ephemeral = false;
      this.showPageNumbers = true;
    }
    else {
      // New constructor: new Pagination(options)
      this.pages = clientOrOptions.embeds ? [{ embeds: clientOrOptions.embeds }] : [];
      this.time = clientOrOptions.time ?? 60_000;
      this.client = clientOrOptions.client!;
      this.ephemeral = clientOrOptions.ephemeral ?? false;
      this.showPageNumbers = clientOrOptions.showPageNumbers ?? true;
    }
  }

  private getEmoji(name: EmojiKeys): string {
    return getEmoji(name, this.client);
  }

  private createButtons(disabled = false): ActionRowBuilder<ButtonBuilder> {
    return new ActionRowBuilder<ButtonBuilder>().addComponents(
      new ButtonBuilder()
        .setCustomId('pagination_first')
        .setEmoji(this.getEmoji('arrow_left'))
        .setStyle(ButtonStyle.Secondary)
        .setDisabled(disabled || this.currentPage === 0),
      new ButtonBuilder()
        .setCustomId('pagination_prev')
        .setEmoji(this.getEmoji('previous'))
        .setStyle(ButtonStyle.Secondary)
        .setDisabled(disabled || this.currentPage === 0),
      new ButtonBuilder()
        .setCustomId('pagination_next')
        .setEmoji(this.getEmoji('next'))
        .setStyle(ButtonStyle.Secondary)
        .setDisabled(disabled || this.currentPage === this.pages.length - 1),
      new ButtonBuilder()
        .setCustomId('pagination_last')
        .setEmoji(this.getEmoji('arrow_right'))
        .setStyle(ButtonStyle.Secondary)
        .setDisabled(disabled || this.currentPage === this.pages.length - 1),
    );
  }

  private updateEmbed(): EmbedBuilder | undefined {
    const page = this.pages[this.currentPage];
    if (!page?.embeds?.[0]) return undefined;

    const embed = page.embeds[0];
    if (this.showPageNumbers && this.pages.length > 1) {
      embed.setFooter({
        text: `Page ${this.currentPage + 1} of ${this.pages.length}`,
        iconURL: embed.data.footer?.icon_url,
      });
    }
    return embed;
  }

  /**
   * Add pages to the pagination (legacy API)
   */
  addPages(pages: any[]): this {
    this.pages = pages.map((page) => {
      if (page.embeds) {
        return { embeds: page.embeds, components: page.components };
      }
      return { embeds: [page] };
    });
    return this;
  }

  /**
   * Add a single page to the pagination
   */
  addPage(page: PageOptions): this {
    this.pages.push(page);
    return this;
  }

  /**
   * Run the pagination (legacy API)
   */
  async run(
    interaction: Interaction,
    options?: { deleteOnEnd?: boolean; idle?: number },
  ): Promise<void> {
    await this.reply(interaction);
  }

  async reply(interaction: Interaction): Promise<InteractionResponse | Message | null> {
    if (!interaction.isRepliable()) return null;

    if (this.pages.length === 0) {
      return await interaction.reply({
        content: 'No data to display.',
        ephemeral: this.ephemeral,
      });
    }

    const currentPage = this.pages[this.currentPage];

    if (this.pages.length === 1) {
      return await interaction.reply({
        embeds: currentPage.embeds || [],
        components: currentPage.components || [],
        ephemeral: this.ephemeral,
      });
    }

    const embed = this.updateEmbed();
    const response = await interaction.reply({
      embeds: embed ? [embed] : currentPage.embeds || [],
      components: [...(currentPage.components || []), this.createButtons()],
      ephemeral: this.ephemeral,
    });

    const message = this.ephemeral ? await interaction.fetchReply() : response;
    if (!message || !('createMessageComponentCollector' in message)) return response;

    const collector = message.createMessageComponentCollector({
      componentType: ComponentType.Button,
      time: this.time,
    });

    collector.on('collect', async (buttonInteraction: MessageComponentInteraction) => {
      if (buttonInteraction.user.id !== interaction.user.id) {
        await buttonInteraction.reply({
          content: 'You cannot interact with this pagination.',
          ephemeral: true,
        });
        return;
      }

      switch (buttonInteraction.customId) {
        case 'pagination_first':
          this.currentPage = 0;
          break;
        case 'pagination_prev':
          this.currentPage = Math.max(0, this.currentPage - 1);
          break;
        case 'pagination_next':
          this.currentPage = Math.min(this.pages.length - 1, this.currentPage + 1);
          break;
        case 'pagination_last':
          this.currentPage = this.pages.length - 1;
          break;
      }

      const currentPage = this.pages[this.currentPage];
      const embed = this.updateEmbed();

      await buttonInteraction.update({
        embeds: embed ? [embed] : currentPage.embeds || [],
        components: [...(currentPage.components || []), this.createButtons()],
      });
    });

    collector.on('end', async () => {
      try {
        await interaction.editReply({
          components: [this.createButtons(true)],
        });
      }
      catch {
        // Ignore errors when editing reply (message might be deleted)
      }
    });

    return response;
  }

  async send(
    interaction: Interaction,
    options?: Omit<InteractionReplyOptions, 'embeds' | 'components'>,
  ): Promise<InteractionResponse | Message | null> {
    if (!interaction.isRepliable()) return null;

    if (this.pages.length === 0) {
      return await interaction.reply({
        content: 'No data to display.',
        ephemeral: this.ephemeral,
        ...options,
      });
    }

    const currentPage = this.pages[this.currentPage];

    if (this.pages.length === 1) {
      return await interaction.reply({
        embeds: currentPage.embeds || [],
        components: currentPage.components || [],
        ephemeral: this.ephemeral,
        ...options,
      });
    }

    const embed = this.updateEmbed();
    return await interaction.reply({
      embeds: embed ? [embed] : currentPage.embeds || [],
      components: [...(currentPage.components || []), this.createButtons()],
      ephemeral: this.ephemeral,
      ...options,
    });
  }
}
