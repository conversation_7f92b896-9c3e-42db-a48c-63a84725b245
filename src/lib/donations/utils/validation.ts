/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { DONATION_PROCESSING, KOFI_CONSTANTS } from './constants.js';
import { validateDonationAmount } from './currency.js';
import type { DonationValidationResult } from '../types/DonationTypes.js';

/**
 * Donation validation utilities
 */

/**
 * Validate Ko-fi webhook verification token
 * @param providedToken Token from Ko-fi webhook
 * @param expectedToken Expected verification token
 * @returns True if token is valid
 */
export function validateKofiToken(
  providedToken: string,
  expectedToken: string,
): boolean {
  if (!providedToken || !expectedToken) {
    return false;
  }

  // Use constant-time comparison to prevent timing attacks
  return providedToken === expectedToken;
}

/**
 * Validate Ko-fi webhook payload structure
 * @param payload Raw Ko-fi payload
 * @returns Validation result
 */
export function validateKofiPayload(payload: any): DonationValidationResult {
  const warnings: string[] = [];

  // Check required fields
  const requiredFields = [
    'kofi_transaction_id',
    'message_id',
    'amount',
    'currency',
    'from_name',
    'timestamp',
  ];

  for (const field of requiredFields) {
    if (!payload[field]) {
      return {
        valid: false,
        error: `Missing required field: ${field}`,
      };
    }
  }

  // Validate amount
  const amount = parseFloat(payload.amount);
  if (isNaN(amount) || amount <= 0) {
    return {
      valid: false,
      error: 'Invalid donation amount',
    };
  }

  // Validate currency and amount
  const amountValidation = validateDonationAmount(amount, payload.currency);
  if (!amountValidation.valid) {
    return {
      valid: false,
      error: amountValidation.error,
    };
  }

  // Check for suspicious amounts
  if (amount > 1000) {
    warnings.push('Large donation amount detected - manual review recommended');
  }

  // Validate transaction ID format
  if (typeof payload.kofi_transaction_id !== 'string' || 
      payload.kofi_transaction_id.length < 10) {
    return {
      valid: false,
      error: 'Invalid Ko-fi transaction ID format',
    };
  }

  // Validate timestamp
  const timestamp = new Date(payload.timestamp);
  if (isNaN(timestamp.getTime())) {
    return {
      valid: false,
      error: 'Invalid timestamp format',
    };
  }

  // Check if timestamp is too old (more than 24 hours)
  const now = new Date();
  const hoursDiff = (now.getTime() - timestamp.getTime()) / (1000 * 60 * 60);
  if (hoursDiff > 24) {
    warnings.push('Donation timestamp is more than 24 hours old');
  }

  // Validate donor name
  if (typeof payload.from_name !== 'string' || payload.from_name.trim().length === 0) {
    return {
      valid: false,
      error: 'Invalid donor name',
    };
  }

  // Validate message length if present
  if (payload.message && payload.message.length > DONATION_PROCESSING.MAX_MESSAGE_LENGTH) {
    return {
      valid: false,
      error: `Donation message too long (max ${DONATION_PROCESSING.MAX_MESSAGE_LENGTH} characters)`,
    };
  }

  // Validate email format if present
  if (payload.email && !isValidEmail(payload.email)) {
    warnings.push('Invalid email format provided');
  }

  return {
    valid: true,
    warnings: warnings.length > 0 ? warnings : undefined,
  };
}

/**
 * Validate donation message content
 * @param message Donation message
 * @returns Validation result
 */
export function validateDonationMessage(message: string): DonationValidationResult {
  if (!message) {
    return { valid: true }; // Empty message is allowed
  }

  // Check length
  if (message.length > DONATION_PROCESSING.MAX_MESSAGE_LENGTH) {
    return {
      valid: false,
      error: `Message too long (max ${DONATION_PROCESSING.MAX_MESSAGE_LENGTH} characters)`,
    };
  }

  // Check for potentially harmful content
  const suspiciousPatterns = [
    /https?:\/\/[^\s]+/gi, // URLs
    /@everyone|@here/gi,   // Discord mentions
    /<@[!&]?\d+>/gi,       // Discord user/role mentions
  ];

  const warnings: string[] = [];
  for (const pattern of suspiciousPatterns) {
    if (pattern.test(message)) {
      warnings.push('Message contains potentially suspicious content');
      break;
    }
  }

  return {
    valid: true,
    warnings: warnings.length > 0 ? warnings : undefined,
  };
}

/**
 * Validate Discord user ID format
 * @param userId Discord user ID
 * @returns True if valid Discord user ID
 */
export function validateDiscordUserId(userId: string): boolean {
  if (!userId || typeof userId !== 'string') {
    return false;
  }

  // Discord user IDs are 17-19 digit snowflakes
  const snowflakePattern = /^\d{17,19}$/;
  return snowflakePattern.test(userId);
}

/**
 * Validate email format
 * @param email Email address
 * @returns True if valid email format
 */
export function isValidEmail(email: string): boolean {
  if (!email || typeof email !== 'string') {
    return false;
  }

  // Basic email validation regex
  const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailPattern.test(email) && email.length <= 254;
}

/**
 * Validate Ko-fi tier name
 * @param tierName Ko-fi membership tier name
 * @returns True if valid tier name
 */
export function validateKofiTierName(tierName: string): boolean {
  if (!tierName || typeof tierName !== 'string') {
    return false;
  }

  // Known Ko-fi tier names (case-insensitive)
  const validTiers = ['supporter', 'gold', 'platinum'];
  return validTiers.includes(tierName.toLowerCase());
}

/**
 * Sanitize donation message for display
 * @param message Raw donation message
 * @returns Sanitized message
 */
export function sanitizeDonationMessage(message: string): string {
  if (!message) {
    return '';
  }

  // Remove potentially harmful content
  return message
    .replace(/https?:\/\/[^\s]+/gi, '[URL]') // Replace URLs
    .replace(/@everyone|@here/gi, '@\u200beveryone') // Zero-width space to break mentions
    .replace(/<@[!&]?\d+>/gi, '[MENTION]') // Replace Discord mentions
    .trim()
    .substring(0, DONATION_PROCESSING.MAX_MESSAGE_LENGTH);
}

/**
 * Validate premium feature access
 * @param userId Discord user ID
 * @param feature Premium feature name
 * @returns Validation result
 */
export function validatePremiumFeatureAccess(
  userId: string,
  feature: string,
): DonationValidationResult {
  if (!validateDiscordUserId(userId)) {
    return {
      valid: false,
      error: 'Invalid Discord user ID',
    };
  }

  if (!feature || typeof feature !== 'string') {
    return {
      valid: false,
      error: 'Invalid premium feature name',
    };
  }

  return { valid: true };
}

/**
 * Validate donation processing environment
 * @returns Validation result
 */
export function validateDonationEnvironment(): DonationValidationResult {
  const warnings: string[] = [];

  // Check required environment variables
  if (!process.env[KOFI_CONSTANTS.VERIFICATION_TOKEN_ENV]) {
    return {
      valid: false,
      error: `Missing required environment variable: ${KOFI_CONSTANTS.VERIFICATION_TOKEN_ENV}`,
    };
  }

  // Check database connection
  // Note: This would typically check if database is accessible
  // For now, we'll assume it's handled elsewhere

  // Check Redis connection for caching
  // Note: This would typically check if Redis is accessible
  // For now, we'll assume it's handled elsewhere

  return {
    valid: true,
    warnings: warnings.length > 0 ? warnings : undefined,
  };
}
