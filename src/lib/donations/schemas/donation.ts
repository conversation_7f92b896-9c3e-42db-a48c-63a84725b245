/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { z } from 'zod';
import { DONATION_PROCESSING, CURRENCY_CONFIG } from '../utils/constants.js';

/**
 * Internal donation data validation schema
 */
export const donationDataSchema = z.object({
  kofiTransactionId: z.string().uuid('Ko-fi transaction ID must be a valid UUID'),
  messageId: z.string().uuid('Message ID must be a valid UUID'),
  amount: z.number()
    .positive('Amount must be positive')
    .min(DONATION_PROCESSING.MIN_DONATION_AMOUNT, `Minimum donation: $${DONATION_PROCESSING.MIN_DONATION_AMOUNT}`)
    .max(DONATION_PROCESSING.MAX_DONATION_AMOUNT, `Maximum donation: $${DONATION_PROCESSING.MAX_DONATION_AMOUNT}`),
  currency: z.string()
    .length(3, 'Currency must be a 3-letter code')
    .refine(
      (currency) => CURRENCY_CONFIG.SUPPORTED_CURRENCIES.includes(currency.toUpperCase() as any),
      'Unsupported currency'
    ),
  fromName: z.string()
    .min(1, 'Donor name cannot be empty')
    .max(100, 'Donor name too long'),
  message: z.string()
    .max(DONATION_PROCESSING.MAX_MESSAGE_LENGTH, `Message too long (max ${DONATION_PROCESSING.MAX_MESSAGE_LENGTH} characters)`)
    .nullable(),
  email: z.string()
    .email('Invalid email format')
    .max(254, 'Email too long')
    .nullable(),
  isPublic: z.boolean(),
  kofiTimestamp: z.date(),
  kofiUrl: z.string()
    .url('Invalid Ko-fi URL')
    .nullable(),
});

/**
 * Donation processing request schema
 */
export const donationProcessingRequestSchema = z.object({
  donationData: donationDataSchema,
  discordUserId: z.string()
    .regex(/^\d{17,19}$/, 'Invalid Discord user ID format')
    .optional(),
  amountUsd: z.number()
    .positive('USD amount must be positive')
    .optional(),
});

/**
 * Premium status check request schema
 */
export const premiumStatusRequestSchema = z.object({
  userId: z.string()
    .regex(/^\d{17,19}$/, 'Invalid Discord user ID format'),
  feature: z.string()
    .min(1, 'Feature name cannot be empty')
    .optional(),
});

/**
 * Donor perk assignment schema
 */
export const donorPerkAssignmentSchema = z.object({
  userId: z.string()
    .regex(/^\d{17,19}$/, 'Invalid Discord user ID format'),
  perkId: z.string()
    .cuid('Invalid perk ID format'),
  duration: z.number()
    .int('Duration must be an integer')
    .positive('Duration must be positive')
    .nullable(),
  expiresAt: z.date().nullable(),
});

/**
 * Media premium grant schema
 */
export const mediaPremiumGrantSchema = z.object({
  userId: z.string()
    .regex(/^\d{17,19}$/, 'Invalid Discord user ID format'),
  duration: z.number()
    .int('Duration must be an integer')
    .positive('Duration must be positive'),
  isSubscription: z.boolean().default(false),
  isFirstPayment: z.boolean().default(false),
});

/**
 * Donation statistics request schema
 */
export const donationStatsRequestSchema = z.object({
  userId: z.string()
    .regex(/^\d{17,19}$/, 'Invalid Discord user ID format')
    .optional(),
  limit: z.number()
    .int('Limit must be an integer')
    .positive('Limit must be positive')
    .max(100, 'Limit too high')
    .default(10),
  offset: z.number()
    .int('Offset must be an integer')
    .min(0, 'Offset cannot be negative')
    .default(0),
});

/**
 * Currency conversion request schema
 */
export const currencyConversionRequestSchema = z.object({
  amount: z.number()
    .positive('Amount must be positive'),
  fromCurrency: z.string()
    .length(3, 'Currency must be a 3-letter code'),
  toCurrency: z.string()
    .length(3, 'Currency must be a 3-letter code')
    .default(CURRENCY_CONFIG.BASE_CURRENCY),
});

/**
 * Donation announcement schema
 */
export const donationAnnouncementSchema = z.object({
  donorName: z.string()
    .min(1, 'Donor name cannot be empty')
    .max(100, 'Donor name too long'),
  amount: z.number()
    .positive('Amount must be positive'),
  currency: z.string()
    .length(3, 'Currency must be a 3-letter code'),
  message: z.string()
    .max(DONATION_PROCESSING.MAX_MESSAGE_LENGTH, 'Message too long')
    .optional(),
  isPublic: z.boolean(),
  isFirstTime: z.boolean(),
});

/**
 * Ko-fi membership tier validation schema
 */
export const membershipTierSchema = z.object({
  tierName: z.string()
    .min(1, 'Tier name cannot be empty')
    .max(50, 'Tier name too long'),
  monthlyPrice: z.number()
    .positive('Monthly price must be positive'),
  features: z.array(z.string())
    .min(1, 'At least one feature required'),
  duration: z.number()
    .int('Duration must be an integer')
    .positive('Duration must be positive'),
});

// Export inferred types
export type DonationDataInput = z.infer<typeof donationDataSchema>;
export type DonationProcessingRequest = z.infer<typeof donationProcessingRequestSchema>;
export type PremiumStatusRequest = z.infer<typeof premiumStatusRequestSchema>;
export type DonorPerkAssignmentInput = z.infer<typeof donorPerkAssignmentSchema>;
export type MediaPremiumGrantInput = z.infer<typeof mediaPremiumGrantSchema>;
export type DonationStatsRequest = z.infer<typeof donationStatsRequestSchema>;
export type CurrencyConversionRequest = z.infer<typeof currencyConversionRequestSchema>;
export type DonationAnnouncementInput = z.infer<typeof donationAnnouncementSchema>;
export type MembershipTierInput = z.infer<typeof membershipTierSchema>;
