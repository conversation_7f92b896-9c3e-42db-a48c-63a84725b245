/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { CacheManager } from '#src/managers/CacheManager.js';
import { DonationManager } from './DonationManager.js';
import Logger from '#src/utils/Logger.js';
import { CACHE_CONFIG } from '../utils/constants.js';
import {
  PremiumFeature,
  PremiumTier,
  type PremiumStatus,
  type PremiumFeatureAccess
} from '../types/PremiumTypes.js';

/**
 * Service for managing premium features and Ko-fi Supporter tier verification
 * Integrates with DonationManager for premium status checks
 */
export class PremiumService {
  private readonly donationManager: DonationManager;
  private readonly cacheManager: CacheManager;

  constructor(donationManager: DonationManager, cacheManager: CacheManager) {
    this.donationManager = donationManager;
    this.cacheManager = cacheManager;
  }

  /**
   * Check if user has Ko-fi Supporter tier access (premium hub features)
   * Uses existing DonationManager.hasMediaPremium() as Ko-fi Supporter tier grants media premium
   */
  async hasSupporterTier(userId: string): Promise<boolean> {
    const cacheKey = `${CACHE_CONFIG.SUPPORTER_TIER_PREFIX}:${userId}`;

    try {
      // Check cache first
      const cached = await this.cacheManager.get(cacheKey);
      if (cached !== null) {
        return cached as boolean;
      }

      // Check premium status using DonationManager
      // Ko-fi Supporter tier grants media premium, so we can use this check
      const hasSupporter = await this.donationManager.hasMediaPremium(userId);

      // Cache the result
      await this.cacheManager.set(cacheKey, hasSupporter, CACHE_CONFIG.PREMIUM_STATUS_TTL);

      Logger.debug(`Premium status check for user ${userId}: ${hasSupporter}`);
      return hasSupporter;
    }
    catch (error) {
      Logger.error(`Failed to check premium status for user ${userId}`, error);
      // Return false on error to fail safely
      return false;
    }
  }

  /**
   * Check if user can use premium hub features (hub name customization)
   * Alias for hasSupporterTier for semantic clarity
   */
  async canCustomizeHubName(userId: string): Promise<boolean> {
    return this.hasSupporterTier(userId);
  }

  /**
   * Check if user has access to a specific premium feature
   */
  async hasFeatureAccess(userId: string, feature: PremiumFeature): Promise<PremiumFeatureAccess> {
    try {
      const hasSupporter = await this.hasSupporterTier(userId);
      const isDonor = await this.donationManager.isUserDonor(userId);
      const totalDonated = await this.donationManager.getUserTotalDonated(userId);

      // Determine user's premium tier
      let tier: PremiumTier = PremiumTier.NONE;
      if (hasSupporter) {
        tier = PremiumTier.SUPPORTER;
      } else if (totalDonated >= 25) {
        tier = PremiumTier.VIP;
      } else if (isDonor) {
        tier = PremiumTier.DONOR;
      }

      // Check feature access based on tier and requirements
      const hasAccess = this.checkFeatureAccess(tier, feature, totalDonated);

      return {
        hasAccess,
        feature,
        tier,
        reason: hasAccess ? undefined : this.getAccessDeniedReason(feature),
        upgradeUrl: hasAccess ? undefined : 'https://ko-fi.com/interchat',
      };
    }
    catch (error) {
      Logger.error(`Failed to check feature access for user ${userId}, feature ${feature}`, error);
      return {
        hasAccess: false,
        feature,
        tier: PremiumTier.NONE,
        reason: 'Error checking premium status',
      };
    }
  }

  /**
   * Invalidate premium status cache for a user
   * Useful when premium status changes (e.g., after Ko-fi webhook)
   */
  async invalidatePremiumCache(userId: string): Promise<void> {
    const cacheKeys = [
      `${CACHE_CONFIG.SUPPORTER_TIER_PREFIX}:${userId}`,
      `${CACHE_CONFIG.PREMIUM_STATUS_PREFIX}:${userId}`,
      `${CACHE_CONFIG.DONATION_STATS_PREFIX}:${userId}`,
    ];

    for (const key of cacheKeys) {
      await this.cacheManager.delete(key);
    }

    Logger.debug(`Invalidated premium cache for user ${userId}`);
  }

  /**
   * Get comprehensive premium feature status summary for a user
   */
  async getPremiumStatus(userId: string): Promise<PremiumStatus> {
    try {
      const [hasSupporter, hasMediaPremium, isDonor, totalDonated] = await Promise.all([
        this.hasSupporterTier(userId),
        this.donationManager.hasMediaPremium(userId),
        this.donationManager.isUserDonor(userId),
        this.donationManager.getUserTotalDonated(userId),
      ]);

      // Determine tier
      let tier: PremiumTier = PremiumTier.NONE;
      if (hasSupporter) {
        tier = PremiumTier.SUPPORTER;
      } else if (totalDonated >= 25) {
        tier = PremiumTier.VIP;
      } else if (isDonor) {
        tier = PremiumTier.DONOR;
      }

      // Get available features for this tier
      const features = this.getFeaturesForTier(tier, totalDonated);

      return {
        hasSupporter,
        canCustomizeHubName: hasSupporter,
        hasMediaPremium,
        tier,
        expiresAt: null, // TODO: Get actual expiration date from database
        features,
      };
    }
    catch (error) {
      Logger.error(`Failed to get premium status for user ${userId}`, error);
      return {
        hasSupporter: false,
        canCustomizeHubName: false,
        hasMediaPremium: false,
        tier: PremiumTier.NONE,
        expiresAt: null,
        features: [],
      };
    }
  }

  /**
   * Log premium feature usage for analytics
   */
  logPremiumFeatureUsage(userId: string, feature: string, hubId?: string): void {
    const context = hubId ? { userId, feature, hubId } : { userId, feature };
    Logger.info(`Premium feature used: ${feature}`, context);
  }

  /**
   * Check if a tier has access to a specific feature
   */
  private checkFeatureAccess(tier: PremiumTier, feature: PremiumFeature, totalDonated: number): boolean {
    switch (feature) {
      case PremiumFeature.UNLIMITED_MEDIA_SHARING:
      case PremiumFeature.HUB_NAME_CUSTOMIZATION:
        return tier === PremiumTier.SUPPORTER;

      case PremiumFeature.DONOR_BADGE:
        return tier !== PremiumTier.NONE;

      case PremiumFeature.PRIORITY_SUPPORT:
        return totalDonated >= 5.0;

      case PremiumFeature.EARLY_ACCESS:
        return totalDonated >= 10.0;

      case PremiumFeature.CUSTOM_PROFILE_THEME:
        return totalDonated >= 15.0;

      case PremiumFeature.VIP_DONOR_BADGE:
        return totalDonated >= 25.0;

      case PremiumFeature.SUPPORTER_BADGE:
        return totalDonated >= 50.0;

      default:
        return false;
    }
  }

  /**
   * Get features available for a specific tier
   */
  private getFeaturesForTier(tier: PremiumTier, totalDonated: number): PremiumFeature[] {
    const features: PremiumFeature[] = [];

    if (tier !== PremiumTier.NONE) {
      features.push(PremiumFeature.DONOR_BADGE);
    }

    if (tier === PremiumTier.SUPPORTER) {
      features.push(
        PremiumFeature.UNLIMITED_MEDIA_SHARING,
        PremiumFeature.HUB_NAME_CUSTOMIZATION,
      );
    }

    if (totalDonated >= 5.0) {
      features.push(PremiumFeature.PRIORITY_SUPPORT);
    }

    if (totalDonated >= 10.0) {
      features.push(PremiumFeature.EARLY_ACCESS);
    }

    if (totalDonated >= 15.0) {
      features.push(PremiumFeature.CUSTOM_PROFILE_THEME);
    }

    if (totalDonated >= 25.0) {
      features.push(PremiumFeature.VIP_DONOR_BADGE);
    }

    if (totalDonated >= 50.0) {
      features.push(PremiumFeature.SUPPORTER_BADGE);
    }

    return features;
  }

  /**
   * Get reason for access denied
   */
  private getAccessDeniedReason(feature: PremiumFeature): string {
    switch (feature) {
      case PremiumFeature.UNLIMITED_MEDIA_SHARING:
        return 'Upgrade to Ko-fi Supporter ($3/month) for unlimited media sharing';

      case PremiumFeature.HUB_NAME_CUSTOMIZATION:
        return 'Hub name customization requires Ko-fi Supporter tier ($3/month)';

      case PremiumFeature.DONOR_BADGE:
        return 'Make any donation to unlock the donor badge';

      case PremiumFeature.PRIORITY_SUPPORT:
        return 'Donate $5+ to unlock priority support';

      case PremiumFeature.EARLY_ACCESS:
        return 'Donate $10+ to get early access to new features';

      case PremiumFeature.CUSTOM_PROFILE_THEME:
        return 'Donate $15+ to unlock custom profile themes';

      case PremiumFeature.VIP_DONOR_BADGE:
        return 'Donate $25+ to unlock the VIP donor badge';

      case PremiumFeature.SUPPORTER_BADGE:
        return 'Donate $50+ to unlock the supporter badge';

      default:
        return 'Premium feature not available';
    }
  }
}
