/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import Logger from '#src/utils/Logger.js';
import { DonationManager } from '../core/DonationManager.js';
import { 
  kofiPayloadSchema, 
  validateKofiWebhookToken,
  isKofiSubscription,
  extractKofiMetadata,
  type KofiPayload 
} from '../schemas/kofi.js';
import { validateKofiPayload, validateDonationEnvironment } from '../utils/validation.js';
import { KOFI_CONSTANTS, ERROR_MESSAGES, SUCCESS_MESSAGES } from '../utils/constants.js';
import type { DonationProcessingResult } from '../types/DonationTypes.js';

/**
 * Service for handling Ko-fi webhook processing
 * Separates webhook logic from core donation management
 */
export class KofiWebhookService {
  private readonly donationManager: DonationManager;

  constructor(donationManager: DonationManager) {
    this.donationManager = donationManager;
  }

  /**
   * Process Ko-fi webhook request
   */
  async processWebhook(
    formData: FormData,
    discordUserId?: string,
  ): Promise<DonationProcessingResult> {
    const startTime = Date.now();

    try {
      // Validate environment
      const envValidation = validateDonationEnvironment();
      if (!envValidation.valid) {
        Logger.error('[kofi] Environment validation failed', { error: envValidation.error });
        return {
          success: false,
          error: ERROR_MESSAGES.DONATION_PROCESSING_FAILED,
        };
      }

      // Extract and validate form data
      const dataField = formData.get(KOFI_CONSTANTS.WEBHOOK_DATA_FIELD);
      if (!dataField || typeof dataField !== 'string') {
        Logger.warn('[kofi] Missing or invalid data field in Ko-fi webhook');
        return {
          success: false,
          error: ERROR_MESSAGES.MISSING_KOFI_DATA,
        };
      }

      // Parse JSON payload
      let kofiPayload: any;
      try {
        kofiPayload = JSON.parse(dataField);
      } catch (error) {
        Logger.error('[kofi] Invalid JSON in Ko-fi webhook data field', error);
        return {
          success: false,
          error: ERROR_MESSAGES.INVALID_JSON_DATA,
        };
      }

      // Validate payload structure
      const payloadValidation = validateKofiPayload(kofiPayload);
      if (!payloadValidation.valid) {
        Logger.error('[kofi] Ko-fi payload validation failed', { error: payloadValidation.error });
        return {
          success: false,
          error: payloadValidation.error || ERROR_MESSAGES.DONATION_PROCESSING_FAILED,
        };
      }

      // Log warnings if any
      if (payloadValidation.warnings) {
        for (const warning of payloadValidation.warnings) {
          Logger.warn(`[kofi] ${warning}`);
        }
      }

      // Verify webhook token
      const expectedToken = process.env[KOFI_CONSTANTS.VERIFICATION_TOKEN_ENV];
      if (!expectedToken) {
        Logger.error('[kofi] KOFI_VERIFICATION_TOKEN not configured');
        return {
          success: false,
          error: ERROR_MESSAGES.DONATION_PROCESSING_FAILED,
        };
      }

      if (!validateKofiWebhookToken(kofiPayload.verification_token, expectedToken)) {
        Logger.warn('[kofi] Unauthorized request - invalid verification token');
        return {
          success: false,
          error: ERROR_MESSAGES.INVALID_KOFI_TOKEN,
        };
      }

      // Parse with Zod schema for type safety
      const parsedPayload = kofiPayloadSchema.parse(kofiPayload);

      // Extract metadata for logging
      const metadata = extractKofiMetadata(parsedPayload);
      const logType = metadata.isSubscription ? 'subscription' : 'donation';

      Logger.info(
        `[kofi] Processing ${logType}: ${parsedPayload.amount} ${parsedPayload.currency} from ${parsedPayload.from_name}${metadata.tierName ? ` (${metadata.tierName} tier)` : ''}`,
      );

      // Process the donation
      await this.donationManager.processDonation(parsedPayload, discordUserId);

      // Log performance
      const processingTime = Date.now() - startTime;
      Logger.info(`[kofi] Successfully processed ${logType} ${parsedPayload.kofi_transaction_id} in ${processingTime}ms`);

      return {
        success: true,
        premiumGranted: metadata.isSubscription && metadata.tierName?.toLowerCase() === 'supporter',
      };
    }
    catch (error) {
      const processingTime = Date.now() - startTime;
      Logger.error(`[kofi] Failed to process webhook in ${processingTime}ms`, error);

      return {
        success: false,
        error: error instanceof Error ? error.message : ERROR_MESSAGES.DONATION_PROCESSING_FAILED,
      };
    }
  }

  /**
   * Validate Ko-fi webhook token
   */
  validateWebhookToken(providedToken: string): boolean {
    const expectedToken = process.env[KOFI_CONSTANTS.VERIFICATION_TOKEN_ENV];
    if (!expectedToken) {
      Logger.error('[kofi] KOFI_VERIFICATION_TOKEN not configured');
      return false;
    }

    return validateKofiWebhookToken(providedToken, expectedToken);
  }

  /**
   * Extract Ko-fi payload from form data
   */
  extractPayloadFromFormData(formData: FormData): { payload: KofiPayload | null; error?: string } {
    try {
      const dataField = formData.get(KOFI_CONSTANTS.WEBHOOK_DATA_FIELD);
      if (!dataField || typeof dataField !== 'string') {
        return {
          payload: null,
          error: ERROR_MESSAGES.MISSING_KOFI_DATA,
        };
      }

      const parsed = JSON.parse(dataField);
      const payload = kofiPayloadSchema.parse(parsed);

      return { payload };
    }
    catch (error) {
      Logger.error('[kofi] Failed to extract payload from form data', error);
      return {
        payload: null,
        error: error instanceof Error ? error.message : ERROR_MESSAGES.INVALID_JSON_DATA,
      };
    }
  }

  /**
   * Check if payload represents a subscription
   */
  isSubscriptionPayload(payload: KofiPayload): boolean {
    return isKofiSubscription(payload);
  }

  /**
   * Get processing summary for a payload
   */
  getProcessingSummary(payload: KofiPayload): {
    type: 'donation' | 'subscription';
    amount: string;
    currency: string;
    donorName: string;
    tierName?: string;
    isFirstPayment?: boolean;
  } {
    const metadata = extractKofiMetadata(payload);

    return {
      type: metadata.isSubscription ? 'subscription' : 'donation',
      amount: payload.amount,
      currency: payload.currency,
      donorName: payload.from_name,
      tierName: metadata.tierName || undefined,
      isFirstPayment: metadata.isFirstPayment,
    };
  }
}
