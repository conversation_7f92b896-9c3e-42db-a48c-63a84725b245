/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import Logger from '#src/utils/Logger.js';
import db from '#utils/Db.js';
import { DonorPerk, UserDonorPerk } from '#src/generated/prisma/client/index.js';
import type { DonorPerkAssignment, UserDonationStats } from '../types/DonationTypes.js';
import { PREMIUM_PRICING } from '../utils/constants.js';

/**
 * Service for managing donor perks and assignments
 * Handles perk eligibility, granting, and expiration
 */
export class DonorPerkService {
  /**
   * Get available donor perks for a user based on their total donations
   */
  async getAvailablePerks(userId: string): Promise<DonorPerk[]> {
    try {
      // Get user's total donation amount
      const user = await db.user.findUnique({
        where: { id: userId },
        select: { totalDonated: true },
      });

      const totalDonated = user?.totalDonated ?? 0;

      // Get perks user qualifies for but doesn't have yet
      const availablePerks = await db.donorPerk.findMany({
        where: {
          isActive: true,
          minimumDonation: { lte: totalDonated },
          NOT: {
            userPerks: {
              some: {
                userId,
                isActive: true,
              },
            },
          },
        },
        orderBy: { minimumDonation: 'asc' },
      });

      return availablePerks;
    }
    catch (error) {
      Logger.error(`Failed to get available perks for user ${userId}`, error);
      return [];
    }
  }

  /**
   * Get user's active donor perks
   */
  async getUserPerks(userId: string): Promise<(UserDonorPerk & { perk: DonorPerk })[]> {
    try {
      return await db.userDonorPerk.findMany({
        where: {
          userId,
          isActive: true,
          OR: [
            { expiresAt: null },
            { expiresAt: { gt: new Date() } },
          ],
        },
        include: { perk: true },
        orderBy: { grantedAt: 'desc' },
      });
    }
    catch (error) {
      Logger.error(`Failed to get user perks for user ${userId}`, error);
      return [];
    }
  }

  /**
   * Grant a donor perk to a user
   */
  async grantPerk(
    userId: string,
    perkId: string,
    duration?: number | null,
  ): Promise<DonorPerkAssignment> {
    try {
      // Check if user already has this perk
      const existingPerk = await db.userDonorPerk.findUnique({
        where: { userId_perkId: { userId, perkId } },
        include: { perk: true },
      });

      if (existingPerk && existingPerk.isActive) {
        return {
          perk: existingPerk.perk,
          granted: false,
          expiresAt: existingPerk.expiresAt,
          reason: 'User already has this perk',
        };
      }

      // Get perk details
      const perk = await db.donorPerk.findUnique({
        where: { id: perkId },
      });

      if (!perk) {
        throw new Error(`Perk not found: ${perkId}`);
      }

      if (!perk.isActive) {
        return {
          perk,
          granted: false,
          expiresAt: null,
          reason: 'Perk is not active',
        };
      }

      // Calculate expiration date
      const expiresAt = duration ? new Date(Date.now() + duration * 24 * 60 * 60 * 1000) : null;

      // Grant the perk
      if (existingPerk) {
        // Reactivate existing perk
        await db.userDonorPerk.update({
          where: { id: existingPerk.id },
          data: {
            isActive: true,
            expiresAt,
            grantedAt: new Date(),
          },
        });
      } else {
        // Create new perk assignment
        await db.userDonorPerk.create({
          data: {
            userId,
            perkId,
            expiresAt,
            isActive: true,
          },
        });
      }

      Logger.info(`[donation] Granted perk ${perk.name} to user ${userId}${expiresAt ? ` (expires: ${expiresAt.toISOString()})` : ' (permanent)'}`);

      return {
        perk,
        granted: true,
        expiresAt,
      };
    }
    catch (error) {
      Logger.error(`Failed to grant perk ${perkId} to user ${userId}`, error);
      throw error;
    }
  }

  /**
   * Remove or expire a donor perk
   */
  async removePerk(userId: string, perkId: string): Promise<boolean> {
    try {
      const result = await db.userDonorPerk.updateMany({
        where: {
          userId,
          perkId,
          isActive: true,
        },
        data: {
          isActive: false,
        },
      });

      if (result.count > 0) {
        Logger.info(`[donation] Removed perk ${perkId} from user ${userId}`);
        return true;
      }

      return false;
    }
    catch (error) {
      Logger.error(`Failed to remove perk ${perkId} from user ${userId}`, error);
      return false;
    }
  }

  /**
   * Process donor perks for a user based on their donation amount
   */
  async processPerksForDonation(userId: string, donationAmountUsd: number): Promise<DonorPerkAssignment[]> {
    try {
      const availablePerks = await this.getAvailablePerks(userId);
      const assignments: DonorPerkAssignment[] = [];

      for (const perk of availablePerks) {
        const assignment = await this.grantPerk(userId, perk.id, perk.duration);
        assignments.push(assignment);
      }

      return assignments;
    }
    catch (error) {
      Logger.error(`Failed to process perks for user ${userId}`, error);
      return [];
    }
  }

  /**
   * Clean up expired donor perks
   */
  async cleanupExpiredPerks(): Promise<number> {
    try {
      const expiredPerks = await db.userDonorPerk.findMany({
        where: {
          expiresAt: { lt: new Date() },
          isActive: true,
        },
        include: { user: true, perk: true },
      });

      let cleanedCount = 0;

      for (const expiredPerk of expiredPerks) {
        await db.userDonorPerk.update({
          where: { id: expiredPerk.id },
          data: { isActive: false },
        });

        Logger.info(
          `[donation] Removed expired perk ${expiredPerk.perk.name} from user ${expiredPerk.userId}`,
        );

        cleanedCount++;
      }

      if (cleanedCount > 0) {
        Logger.info(`[donation] Cleaned up ${cleanedCount} expired donor perks`);
      }

      return cleanedCount;
    }
    catch (error) {
      Logger.error('Failed to cleanup expired donor perks', error);
      return 0;
    }
  }

  /**
   * Get donor perk statistics
   */
  async getPerkStatistics(): Promise<{
    totalPerks: number;
    activePerks: number;
    totalAssignments: number;
    activeAssignments: number;
    expiredAssignments: number;
  }> {
    try {
      const [
        totalPerks,
        activePerks,
        totalAssignments,
        activeAssignments,
        expiredAssignments,
      ] = await Promise.all([
        db.donorPerk.count(),
        db.donorPerk.count({ where: { isActive: true } }),
        db.userDonorPerk.count(),
        db.userDonorPerk.count({ where: { isActive: true } }),
        db.userDonorPerk.count({
          where: {
            isActive: false,
            expiresAt: { lt: new Date() },
          },
        }),
      ]);

      return {
        totalPerks,
        activePerks,
        totalAssignments,
        activeAssignments,
        expiredAssignments,
      };
    }
    catch (error) {
      Logger.error('Failed to get perk statistics', error);
      return {
        totalPerks: 0,
        activePerks: 0,
        totalAssignments: 0,
        activeAssignments: 0,
        expiredAssignments: 0,
      };
    }
  }

  /**
   * Check if user has a specific perk
   */
  async userHasPerk(userId: string, perkName: string): Promise<boolean> {
    try {
      const userPerk = await db.userDonorPerk.findFirst({
        where: {
          userId,
          isActive: true,
          perk: { name: perkName },
          OR: [
            { expiresAt: null },
            { expiresAt: { gt: new Date() } },
          ],
        },
      });

      return !!userPerk;
    }
    catch (error) {
      Logger.error(`Failed to check if user ${userId} has perk ${perkName}`, error);
      return false;
    }
  }
}
